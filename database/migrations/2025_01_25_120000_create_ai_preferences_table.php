<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('ai_preferences', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('inquiry_id');
            $table->unsignedBigInteger('client_id');
            $table->tinyInteger('client_type')->comment('1=Investor, 2=Client');
            $table->text('summary');
            $table->text('reason');
            $table->json('preferences_json');
            $table->decimal('confidence', 3, 2)->comment('AI confidence score 0.00-1.00');
            $table->timestamps();

            $table->index(['inquiry_id']);
            $table->index(['client_id', 'client_type']);
            $table->index(['created_at']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('ai_preferences');
    }
};
