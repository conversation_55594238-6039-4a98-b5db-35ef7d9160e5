<?php

namespace App\Providers;
use App\Services\BaseAiService;
use App\Services\AiMailService;
use App\Services\AiLoggerService;
use App\Services\CalendarAvailabilityService;
use App\Services\AppointmentService;
use App\Services\DetectIntentService;
use Illuminate\Support\ServiceProvider;

class AiServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        // Register AiLoggerService as a singleton
        $this->app->singleton(AiLoggerService::class);

        // Register BaseAiService as a singleton
        $this->app->singleton(BaseAiService::class);

        // Register CalendarAvailabilityService
        $this->app->singleton(CalendarAvailabilityService::class);

        // Register AppointmentService
        $this->app->singleton(AppointmentService::class);

        // Register DetectIntentService
        $this->app->singleton(DetectIntentService::class);

        // Inject services into AiMailService
        $this->app->singleton(AiMailService::class, function ($app) {
            return new AiMailService(
                $app->make(CalendarAvailabilityService::class),
                $app->make(AppointmentService::class),
                $app->make(AiLoggerService::class)
            );
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}