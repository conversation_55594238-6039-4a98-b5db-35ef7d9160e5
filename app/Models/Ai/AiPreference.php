<?php

namespace App\Models\Ai;

use Illuminate\Database\Eloquent\Model;
use App\Models\Client;
use App\Models\Investors;

class AiPreference extends Model
{
    protected $table = 'ai_preferences';
    
    protected $fillable = [
        'inquiry_id',
        'client_id',
        'client_type',
        'summary',
        'reason',
        'preferences_json',
        'confidence'
    ];

    protected $casts = [
        'preferences_json' => 'array',
        'confidence' => 'decimal:2'
    ];

    public function client()
    {
        return $this->belongsTo(Client::class, 'client_id');
    }

    public function investor()
    {
        return $this->belongsTo(Investors::class, 'client_id');
    }

    public function getClientOrInvestorAttribute()
    {
        return $this->client_type == 2 ? $this->client : $this->investor;
    }
}
