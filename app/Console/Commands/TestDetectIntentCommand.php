<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\DetectIntentService;

class TestDetectIntentCommand extends Command
{
    protected $signature = 'test:detect-intent';
    protected $description = 'Test the DetectIntent service functionality';

    protected $detectIntentService;

    public function __construct(DetectIntentService $detectIntentService)
    {
        parent::__construct();
        $this->detectIntentService = $detectIntentService;
    }

    public function handle()
    {
        $this->info('Testing DetectIntent Service...');

        // Test 1: Customer wants to change preferences
        $this->info('=== TEST 1: Customer wants to change preferences ===');
        $testEmailData1 = [
            'message' => 'Hallo, ich suche jetzt eine Wohnung in München statt Berlin. Mein Budget ist jetzt 500.000-700.000 Euro. Können Sie mir neue Angebote schicken?',
            'inquiry_id' => 12345,
            'client' => [
                'id' => 1,
                'first_name' => 'Test',
                'surname' => 'User',
                'email' => '<EMAIL>'
            ],
            'client_type' => 2,
            'previous_conversations' => []
        ];

        $this->testEmail($testEmailData1);

        $this->info('');
        $this->info('=== TEST 2: Customer shows interest in current property ===');
        $testEmailData2 = [
            'message' => 'Vielen Dank für das Angebot! Die Wohnung sieht sehr interessant aus. Können wir einen Besichtigungstermin vereinbaren? Ich hätte gerne mehr Informationen über die Ausstattung.',
            'inquiry_id' => 12346,
            'client' => [
                'id' => 2,
                'first_name' => 'Test2',
                'surname' => 'User2',
                'email' => '<EMAIL>'
            ],
            'client_type' => 2,
            'previous_conversations' => []
        ];

        $this->testEmail($testEmailData2);

        $this->info('');
        $this->info('Test completed.');
    }

    private function testEmail(array $emailData)
    {
        $this->info('Email message: ' . $emailData['message']);
        $this->info('');

        // Test analysis
        $result = $this->detectIntentService->analyzeEmailContent($emailData);

        if ($result) {
            $this->info('Analysis Result:');
            $this->info('Wants preference change: ' . ($result['wants_preference_change'] ? 'YES' : 'NO'));
            $this->info('Confidence: ' . ($result['confidence'] * 100) . '%');
            $this->info('Summary: ' . $result['summary']);
            $this->info('Reason: ' . $result['reason']);
            $this->info('New preferences: ' . json_encode($result['new_preferences'], JSON_PRETTY_PRINT));

            if ($result['wants_preference_change'] && $result['confidence'] >= 0.9) {
                $this->info('');
                $this->info('✅ This would trigger preference change processing!');
            } else {
                $this->info('');
                $this->info('ℹ️ This would NOT trigger preference change (confidence too low or no change detected)');
            }
        } else {
            $this->error('Analysis failed or returned null');
        }
        $this->info('');
    }
}
