<?php

namespace App\Console\Commands\Match;

use App\Models\Client;
use App\Models\Investors;
use App\Models\Distance\DebugDistanceMatchResults;
use App\Models\Distance\DistanceBuyer;
use App\Models\Distance\DistanceBuyerAddress;
use App\Models\Distance\DistanceMatchResults;
use App\Models\PropertiesNew;
use App\Models\PropertiesNewDetails;
use App\Models\Zipcode;
use App\Models\Input\ConstOpportunityInput;
use App\Models\Input\ConstPropertyCheckbox;


use App\Services\DistanceMatrixService;
use App\Services\CoordinateService;
use App\Models;
use App\Models\BuyerPropertyPreferences;




use Illuminate\Console\Command;

class PropertiesMatch extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'match:properties {--client-id= : Run matching for specific client only}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    protected CoordinateService $coordinateService;

    public function __construct(CoordinateService $coordinateService)
    {
        parent::__construct();
        $this->coordinateService = $coordinateService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $chechboxs = ConstPropertyCheckbox::get();
        $inputs = ConstOpportunityInput::get();

        $clientId = $this->option('client-id');

        $buyers = DistanceBuyer::with('preferences')
        ->whereNull('anzeigen_type'); //sadece kaufen için, mieten yok

        if ($clientId) {
            $buyers = $buyers->where('client_id', $clientId);
        }

        $buyers = $buyers->get();

        foreach ($buyers as $buyer) {

            if($buyer->prospective_id){
                $isActive = Client::where('id', $buyer->client_id)->exists();
                $clientStatus = 1;
            }else{
                $isActive = Investors::where('id', $buyer->client_id)->exists();
            }

            if(!$isActive){
                continue;
            }

            $buyer['addresses'] = DistanceBuyerAddress::where('buyer_id', $buyer->id)->get();
            
            if($buyer['addresses']->isEmpty()){
                continue;
            }

            foreach ($buyer['addresses'] as $address) {
                if (in_array($buyer->preferences->property_type_id, [9, 10, 11, 12, 13])) {
                    $buyerPropertyType = 9;

                    switch ($buyer->preferences->property_type_id) {
                        case 9:
                            $buyerMatchId = 190;
                            break;
                        case 10:
                            $buyerMatchId = 191;
                            break;
                        case 11:
                            $buyerMatchId = 192;
                            break;
                        case 12:
                            $buyerMatchId = 193;
                            break;
                        case 13:
                            $buyerMatchId = 194;
                            break;
                    }

                }else{
                    $buyerPropertyType = $buyer->preferences->property_type_id;
                }

                $query = PropertiesNewDetails::query()->with('property')
                    ->where('property_type_id', $buyerPropertyType);


                if (isset($buyer->preferences->price)) {
                    $priceRange = $buyer->preferences->price;
                
                    if (strpos($priceRange, '-') !== false) {
                        [$minPrice, $maxPrice] = explode('-', $priceRange);
                
                        $minPrice = (int) str_replace('.', '', trim($minPrice));
                        if($minPrice < 10){
                            $minPrice = 20;
                        }
                        $maxPrice = (int) str_replace('.', '', trim($maxPrice));

                        if (($minPrice !== 0 || $maxPrice !== 0) && $maxPrice >= $minPrice) {
                            $query->whereHas('property', function ($q) use ($minPrice, $maxPrice) {
                                $q->where(function ($subQuery) use ($minPrice, $maxPrice) {
                                    $subQuery->where(function ($inner) use ($minPrice, $maxPrice) {
                                        $inner->where('price', '>=', 2)
                                              ->whereBetween('price', [$minPrice, $maxPrice]);
                                    })->orWhere(function ($inner) use ($minPrice, $maxPrice) {
                                        $inner->where('price', '<', 2)
                                              ->whereBetween('indicative', [$minPrice, $maxPrice]);
                                    });
                                });
                            });
                        }
                
                        //echo "Min Price: $minPrice, Max Price: $maxPrice\n";
                    }
                }

                $minLivingSpace = null;
                $maxLivingSpace = null;
                
                if (isset($buyer->preferences->living_space) && strpos($buyer->preferences->living_space, '-') !== false) {
                    [$minLivingSpace, $maxLivingSpace] = explode('-', $buyer->preferences->living_space);
                
                    $minLivingSpace = (int) $minLivingSpace;
                    $maxLivingSpace = (int) $maxLivingSpace;

                    if ($minLivingSpace !== 0 || $maxLivingSpace !== 0) {
                        $query->whereBetween('living_space', [$minLivingSpace, $maxLivingSpace]);
                        //echo "Min LivingSpace: $minLivingSpace, Max LivingSpace: $maxLivingSpace\n";
                    }
                }

                $minTotalSpace = null;
                $maxTotalSpace = null;
                
                if (isset($buyer->preferences->total_space) && strpos($buyer->preferences->total_space, '-') !== false) {
                    [$minTotalSpace, $maxTotalSpace] = explode('-', $buyer->preferences->total_space);
                
                    $minTotalSpace = (int) $minTotalSpace;
                    $maxTotalSpace = (int) $maxTotalSpace;
                
                    if (!is_null($minTotalSpace) && !is_null($maxTotalSpace) && $minTotalSpace !== 0 || $maxTotalSpace !== 0) {
                        $query->whereBetween('total_space', [$minTotalSpace, $maxTotalSpace]);
                        //echo "Min TotalSpace: $minTotalSpace, Max TotalSpace: $maxTotalSpace\n";
                    }

                }
                
                $minRoomNumber = null;
                $maxRoomNumber = null;
                
                if (isset($buyer->preferences->room_number) && strpos($buyer->preferences->room_number, '-') !== false) {
                    [$minRoomNumber, $maxRoomNumber] = explode('-', $buyer->preferences->room_number);
                
                    $minRoomNumber = (int) $minRoomNumber;
                    $maxRoomNumber = (int) $maxRoomNumber;

                    if ($minRoomNumber !== 0 || $maxRoomNumber !== 0) {
                        $query->whereBetween('room_number', [$minRoomNumber, $maxRoomNumber]);
                        //echo "Min RoomNumber: $minRoomNumber, Max RoomNumber: $maxRoomNumber\n";
                    }

                }

                $minConstructionYear = null;
                $maxConstructionYear = null;
                
                if (isset($buyer->preferences->baujahr) && strpos($buyer->preferences->baujahr, '-') !== false) {
                    [$minConstructionYear, $maxConstructionYear] = explode('-', $buyer->preferences->baujahr);
                
                    $minConstructionYear = (int) $minConstructionYear;
                    $maxConstructionYear = (int) $maxConstructionYear;

                    if (!is_null($minConstructionYear) && !is_null($maxConstructionYear) && $minConstructionYear !== 0 || $maxConstructionYear !== 0) {
                        $query->whereBetween('construction_year', [$minConstructionYear, $maxConstructionYear]);
                        //echo "Min ConstructionYear: $minConstructionYear, Max ConstructionYear: $maxConstructionYear\n";
                    }

                }

                $minMietePrice = null;
                $maxMietePrice = null;
                
                if (isset($buyer->preferences->miete_price) && strpos($buyer->preferences->miete_price, '-') !== false) {
                    [$minMietePrice, $maxMietePrice] = explode('-', $buyer->preferences->miete_price);
                
                    $minMietePrice = (int) $minMietePrice;
                    $maxMietePrice = (int) $maxMietePrice;

                    if ($minMietePrice !== 0 || $maxMietePrice !== 0) {
                        $query->whereBetween('miete_price', [$minMietePrice, $maxMietePrice]);
                        //echo "Min MietePrice: $minMietePrice, Max MietePrice: $maxMietePrice\n";
                    }

                }

                $minMetrePrice = null;
                $maxMetrePrice = null;

                if (isset($buyer->preferences->metre_price) && strpos($buyer->preferences->metre_price, '-') !== false) {
                    [$minMetrePrice, $maxMetrePrice] = explode('-', $buyer->preferences->metre_price);
                
                    $minMetrePrice = (int) $minMetrePrice;
                    $maxMetrePrice = (int) $maxMetrePrice;
                    if (!is_null($minMetrePrice) && !is_null($maxMetrePrice) && $minMetrePrice !== 0 || $maxMetrePrice !== 0) {
                        $query->whereBetween('metre_price', [$minMetrePrice, $maxMetrePrice]);
                        //echo "Min MetrePrice: $minMetrePrice, Max MetrePrice: $maxMetrePrice\n";
                    }

                }

                $propertiesDetails = $query
                    ->whereHas('property', function ($q) {
                        $q->whereNull('deleted_at');
                    })
                    ->with('property')
                    ->get();

                

                $client_type = 1;

                if ($buyer->prospective_id > 0) {
                    $client_type = 2; 
                }

                foreach($propertiesDetails as $propertiesDetail){

                    if ($this->isMatched($buyer, $propertiesDetail->property->id)) {
                        var_dump("Daha önce eşleşmiş: " . $buyer->id . "- Property ID: " . $propertiesDetail->property->id);
                        continue;
                    }

                    $finalInputIds = [];

                    if (isset($buyer->preferences->selected_checkbox_id)) {
                        $selectedIds = explode(',', $buyer->preferences->selected_checkbox_id);


                        foreach ($selectedIds as $checkboxId) {
                            $checkbox = $chechboxs->firstWhere('id', (int) $checkboxId);
                        
                            if ($checkbox && $checkbox->input_id) {
                                $inputId = $checkbox->input_id;
                        
                                if (!isset($finalInputIds[$inputId])) {
                                    $finalInputIds[$inputId] = [];
                                }
                                if((int)$checkbox->match_id){
                                    $input = $inputs->firstWhere('match_id',  (int)$checkbox->match_id);
                                    $finalInputIds[$inputId][] = (int)$input->id;
                                }

                            }
                        }
                    }

                    $featureIds = array_filter(explode(',', $propertiesDetail->features_id));

                    //Aşağıdaki ilk if subType kontrolü yapılıyor ****karıştırılmamalı*****
                    //örnek olarak gewerbeobject tipininin subTypeları. 
                    //buyerMatchId içerisinde featureIds içerisine kayıtlanan ıd'ler tanımlı
                    if(isset($buyerMatchId)){
                        if (!in_array($buyerMatchId, $featureIds)) {
                            continue;
                        }
                    }


                    if (!empty($finalInputIds)) {
                        $skip = false; 
                    
                        foreach ($finalInputIds as $inputGroup) {

                            if (empty($inputGroup)) {
                                continue;
                            }

                            $matchedInGroup = false;
                    
                            foreach ($inputGroup as $checkboxId) {
                                if (in_array($checkboxId, $featureIds)) {
                                    $matchedInGroup = true;
                                    break; 
                                }
                            }
                    
                            if (!$matchedInGroup) {
                                
                                $skip = true;
                                break;
                            }
                        }
                    
                        if ($skip) {
                            continue;
                        }
                    }


                    if (isset($buyer->preferences->energy_class)) {
                        $energy_classes = explode(',', $buyer->preferences->energy_class);
                        $matchedEnergyClass = false;
                    
                        foreach ($energy_classes as $energy_class) {
                            $inputEnergy = $inputs->where('property_sub_type', $energy_class)
                                ->where('type', $buyer->preferences->property_type_id)
                                ->first();
                    
                            if ($inputEnergy && in_array($inputEnergy->id, $featureIds)) {
                                $matchedEnergyClass = true; 
                                break;
                            }
                        }
                    
                        if (!$matchedEnergyClass) {
                            continue;
                        }
                    }

                    if (isset($buyer->preferences->net_speed)) {
                        $net_speed = $buyer->preferences->net_speed;
                    
                        $inputNetSpeed = $inputs->where('property_sub_type', $net_speed)
                            ->where('type', $buyer->preferences->property_type_id)
                            ->first();

                        if($inputNetSpeed){
                            if (!in_array($inputNetSpeed->id, $featureIds)) {
                                continue;
                            }
                        }

                    }

                    //burası client yada yatırımcı farketmeksizin kaufen de seçilen kiralık durum kriterine göre eşleme yapıyor.
/*                     if (isset($buyer->preferences->rental_status) && $propertiesDetail->property) {
                        $rental_status = $buyer->preferences->rental_status;

                        if($rental_status === "vermietet"){
                            if((int) $propertiesDetail->property->property_status !== 2){
                                continue;
                            }
                        }   

                        if($rental_status === "unvermietet"){
                            if((int) $propertiesDetail->property->property_status === 2){
                                continue;
                            }
                        }

                    } */

                    //client kullanıcısı için kiralık olmayanları eşleyecek, yatırımcı için normal eşleşmeye devam edecek. 5Haziran.
                    if(isset($clientStatus) && $clientStatus == 1){

                        if((int) $propertiesDetail->property->property_status === 2){
                            continue;
                        }
                    }else{

                        if (isset($buyer->preferences->rental_status) && $propertiesDetail->property) {
                            $rental_status = $buyer->preferences->rental_status;

                            if($rental_status === "vermietet"){
                                if((int) $propertiesDetail->property->property_status !== 2){
                                    continue;
                                }
                            }   

                            if($rental_status === "unvermietet"){
                                if((int) $propertiesDetail->property->property_status === 2){
                                    continue;
                                }
                            }

                        }
                    }

                    if (!empty($address->postal_code) && is_numeric($address->postal_code)) {

                        $postalCode = $address->postal_code;
                        $plz = Zipcode::where('id', $propertiesDetail->property->postal_code)->pluck('code')->first();

                        if (is_numeric($address->radius) && $address->radius > 0) {

                            if(!$plz){
                                continue;
                            }

                            if (!ctype_digit((string)$plz)) {
                                var_dump($plz.' hatalı plz');
                                continue;

                            }
                            $origin = $this->coordinateService->fetchCoordinates($address->postal_code); 
                            $target = $this->coordinateService->fetchCoordinates($plz);

                            if($origin && $target){
                                $distance = $this->coordinateService->calculateDistance(
                                    $origin['lat'],
                                    $origin['lon'],
                                    $target['lat'],
                                    $target['lon']
                                );

                            }else{
                                continue;

                            }


                            if ($distance > $address->radius) {
                                if ($address->postal_code != $plz) {
                                    var_dump("eşleşme olmadı");
                                    continue;
                                }
                            }
                    
                        } else {
                            if ($postalCode != $plz) {
                                continue;
                            }
                        } 
                    }

                    $bestMatchScore = 100;
                    if($propertiesDetail->property->price < 20){
                        $propertyPrice = $propertiesDetail->property->indicative;
                    }else{
                        $propertyPrice = $propertiesDetail->property->price;
                    }

                    $bestMatchDetails = [
                        'Location' => 80,
                        'Preis' => $propertyPrice,
                        'QM' => 0,
                        'Rooms' => $propertiesDetail->room_number ?? null,
                        'Condition' => 0
                    ];
                    
                    DistanceMatchResults::updateOrCreate(
                        [
                            'buyer_id' => $buyer->id,
                            'property_id' => $propertiesDetail->properties_new_id,
                            'client_id' => $buyer->client_id,
                            'client_type' => $client_type
                        ],
                        [
                            'score' => number_format($bestMatchScore, 3),
                            'details' => json_encode($bestMatchDetails)
                        ]
                    );
                    var_dump($propertiesDetail->properties_new_id.' ile '.$buyer->id."Eşleşti.");
                    
                }

            }
        }
        print_r("The matches have been added to the table.");
    }

    public function isMatched($buyer, $propertyId)
    {
        $client_type = 1;

        if ($buyer->prospective_id > 0) {
            $client_type = 2;
        }
        
        return DistanceMatchResults::where('property_id', $propertyId)
            ->where('client_id', $buyer->client_id)
            ->where('client_type', $client_type)
            ->where('buyer_id', $buyer->id)
            ->exists();
    }

    public function inputMatchCheck(){
        $chechboxs = ConstPropertyCheckbox::get();
        $inputs = ConstOpportunityInput::get();

        

        foreach ($chechboxs as $chechbox) {
            if (is_null($chechbox->match_id)) {
                continue;
            }

            foreach ($inputs as $input) {
                if ($chechbox->match_id == $input->match_id) {
                    var_dump($input->id . 'Checkbox property_sub_type: '. $chechbox->property_sub_type. "------". "Input property_sub_type: ".$input->property_sub_type);

                }
            }
        }

        return;
    }
}
