<?php

namespace App\Console\Commands\AI;

use App\Services\AiMailService;
use App\Services\CalendarAvailabilityService;
use App\Services\AiLoggerService;
use App\Services\TestEmailService;
use App\Services\DetectIntentService;
use App\Models\Ai\AiSystemLog;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;
use Webklex\IMAP\Facades\Client;
use App\Models\Ai\CustomerEmailReply;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;

// use App\Models\Calendar;


class FetchOutlookEmails extends Command
{
    protected $signature = 'email:fetch
                            {--test-mode : test modu /Services/TestEmailService e bakiniz}
                            {--no-test-mode : Productionda degilken de test modunu kapatiyor}
                            {--folder=INBOX : fetch yapilacak klasor default INBOX}
                            {--auto-folder : Test moduysa ona gore klasor secer}
                            {--list-folders : IMAP klasorlerini listeleme}
                            {--simple-list : sadece klasor isimleri (say<PERSON><PERSON> olmadan)}';
    protected $description = 'okunmamis mailleri alir eslesenlere yapay zeka cevabini gonderir';

    protected $aiMailService;
    protected $calendarAvailabilityService;
    protected $aiLogger;
    protected $testEmailService;
    protected $detectIntentService;

    public function __construct(
        AiMailService $aiMailService,
        CalendarAvailabilityService $calendarAvailabilityService,
        AiLoggerService $aiLogger,
        TestEmailService $testEmailService,
        DetectIntentService $detectIntentService
    ) {
        parent::__construct();
        $this->aiMailService = $aiMailService;
        $this->calendarAvailabilityService = $calendarAvailabilityService;
        $this->aiLogger = $aiLogger;
        $this->testEmailService = $testEmailService;
        $this->detectIntentService = $detectIntentService;
    }

    public function handle()
    {
        // test modunu belirle
        if ($this->option('no-test-mode')) {
            $testMode = false;
        } else {
            $testMode = $this->option('test-mode');
        }

        // Handle klasor listesi
        if ($this->option('list-folders')) {
            return $this->listFolders();
        }

        if ($this->option('simple-list')) {
            return $this->listFoldersSimple();
        }

        if ($testMode) {
            $this->warn('🧪 TEST MODU');
        }

        try {
            $client = Client::account('ai_mail');
            $client->connect();

            // klasoru sec, test modu ve ya flaga gore default=INBOX
            $folderName = $this->determineFolderName($testMode);
            $folder = $client->getFolder($folderName);

            if (!$folder) {
                $this->info("💡 Available folders:");
                $folders = $client->getFolders();
                foreach ($folders as $availableFolder) {
                    $this->line("   📂 " . $availableFolder->name);
                }
                return;
            }

            // okunmamislarin hepsi
            $messages = $folder->query()->unseen()->get();

            if ($messages->count() == 0) {
                $this->info("📭 No new emails found.");
                return;
            }

            foreach ($messages as $message) {
                $messageId = $message->getMessageId();
                Log::debug("Processing message", [
                    'message_id' => $messageId,
                    'subject' => $message->getSubject(),
                    'flags' => $message->getFlags(),
                    'uid' => $message->getUid(),
                    'msgn' => $message->getMsgn(),
                ]);

                // Okundu isaretle
                $message->setFlag(['Seen']);

                try {
                    // butun yapay zeka sureci baslar
                    $this->processEmail($message, $testMode);
                } catch (\Exception $e) {
                    DB::table('failed_email_processes')->insert([
                        'message_id' => $message->getMessageId(),
                        'from_address' => $message->getFrom()[0]->mail ?? null,
                        'subject' => $message->getSubject() ?? null,
                        'inquiry_id' => $this->extractInquiryId($message->getSubject() ?? '') ?? null,
                        'error' => $e->getMessage(),
                        'created_at' => now(),
                    ]);

                    $this->error("❌ Failed to process email: " . $e->getMessage());
                    continue; // siradaki e mail
                }
            }

        } catch (\Exception $e) {
            Log::error("IMAP Connection Failed", ['error' => $e->getMessage()]);
        }
    }

    /**
     * Process an individual email message
     *
     * @param \Webklex\PHPIMAP\Message $message
     * @param bool $testMode
     * @return void
     */
    protected function processEmail($message, $testMode = false)
    {
        try {
            // subject decode
            $originalSubject = $message->getSubject();
            $decodedSubject = iconv_mime_decode($originalSubject,
                ICONV_MIME_DECODE_CONTINUE_ON_ERROR,
                'UTF-8'
            );
            
            // encode dan kalan kisimlari temizle
            $subject = preg_replace(
                '/=\?UTF-8\?[QB]\?(.*?)\?=/',
                '$1',
                $decodedSubject
            );

            // Terminalde gostermek istenirse UTF-8
            $displaySubject = mb_convert_encoding($subject, 'UTF-8', mb_detect_encoding($subject));

           // $this->info("\n📧 New Email (ID: {$message->getMessageId()}):");
           // $this->info("➡ Subject: " . $displaySubject);


            $from = $message->getFrom();
            $fromAddress = $from[0]->mail ?? null;
            $this->info("➡ From: " . $fromAddress);


            // biz baslatmadiysak atliyoruz (tanimadigimiz emailler ve spam icin)
            $weInitiatedConversation = CustomerEmailReply::where('to_address', $fromAddress)
                ->where('is_outgoing', 1)
                ->exists();

            if (!$weInitiatedConversation) {
                $this->info("❌ No outgoing email found to this address. We haven't initiated conversation with {$fromAddress}. Skipping response.");
                return;
            }

            $this->info("✅ Found existing conversation initiated by us with {$fromAddress}");
            $this->info("🔄 Processing reply from client we contacted first");

            $text_body = $message->getTextBody();
            $html_body = $message->getHTMLBody();
            $stripped_html = strip_tags($html_body);

            $body = '';
            if (!empty($text_body)) {
                $body = $text_body;
            } elseif (!empty($stripped_html)) {
                $body = $stripped_html;
            } elseif (!empty($html_body)) {
                $body = $html_body;
            }

            if (empty($body)) {
                Log::info('Empty email body', [
                    'from_address' => $fromAddress,
                    'subject' => $subject,
                    'stripped_html' => $stripped_html,
                    'html_body' => $html_body,
                    'text_body' => $text_body
                ]);
                // Mail::mailer('ai_mail')->to('<EMAIL>')->bcc('<EMAIL>')->send(new \App\Mail\AiGeneratedResponse(
               //     "AI Email Fetch Error {$fromAddress} : {$subject}",
               //     "Empty email body, AI calismayi durdurdu,<br> stripped : {$stripped_html} <br> html:{$html_body} <br> text:{$text_body}",
               //     []
               // ));
                return;
            }

            // UTF-8 encoding check (this part is good)
            if (!mb_check_encoding($body, 'UTF-8')) {
                $body = mb_convert_encoding($body, 'UTF-8', mb_detect_encoding($body, ['UTF-8', 'ISO-8859-1', 'Windows-1252']));
            }

            $this->info("➡  Body: " . $body);

            // Get the sender's email address
            $from = $message->getFrom();
            $fromAddress = $from[0]->mail ?? null;

            // Clean email content by removing quoted replies
            try {
                $body = $this->removeAfterPattern($body, "From:");
            } catch (\Exception $e) {
                // Devam
            }

            // Sonia | Main Immobilien Compass sonrasini sil, alintilari almamak icin
            try {
                $body = $this->removeAfterPattern($body, "&lt;Sonia | Main Immobilien Compass&gt;");
            } catch (\Exception $e) {
                // Devam
            }


            Log::info('Processing body', [
                'body'=> $body
            ]);

            // hatali oldugu icin kaldirdik, sonra bakabiliriz
            //   $body = $this->aiMailService->cleanEmailContent($body);

            // Konuda var ise  InquiryID ve Property ID yi almaya calisiyoruz
            $inquiryId = $this->extractInquiryId($subject);
            $propertyId = $this->extractPropertyId($subject);
            $inquiryIdFromSubject = (bool) $inquiryId; // Track if inquiry ID came from subject

            // Konu kisminda olmazsa bu kisiyle son maillesmeye bakilarak bulmayi deniyoruz
            // 'Yanitla' yapmadan cevap yazan musterileri yakalamak icin burasi
            if (!$inquiryId) {
                $lastOutgoingEmail = CustomerEmailReply::where('to_address', $fromAddress)
                    ->where('is_outgoing', 1)
                    ->orderBy('created_at', 'desc')
                    ->first();

                if ($lastOutgoingEmail) {
                    $inquiryId = $lastOutgoingEmail->inquiry_id;
                    $propertyId = $lastOutgoingEmail->property_id;
                }
            }
/*
            Log::info('Processing email', [
                'original_subject' => $originalSubject,
                'normalized_subject' => $subject,
                'from' => $fromAddress,
                'inquiry_id' => $inquiryId,
                'property_id' => $propertyId,
                'inquiry_source' => $inquiryId ? ($inquiryIdFromSubject ? 'subject' : 'last_outgoing_email') : 'not_found'
            ]);
*/

            // Inquiry ID varsa devam
            if ($inquiryId) {
                $inquirySource = $inquiryIdFromSubject ? 'subject' : 'last outgoing email';

                // maklere yukselttiysek duracak
                $isEscalated = CustomerEmailReply::where('inquiry_id', $inquiryId)
                    ->whereNotNull('escalated_at')
                    ->exists();

                if ($isEscalated) {
                    $this->info("🚫 Inquiry #{$inquiryId} Maklere gitmis, Duruyor");
                    return;
                }

                // Randevu sartlari kabul edildiyse isaretleniyor ilerde kullanilacak
                $isBooked = CustomerEmailReply::where('inquiry_id', $inquiryId)
                    ->where('appointment_status', 'like', '%APPOINTMENT_BOOKED%')
                    ->exists();
            } else {
                $this->info("No inquiry ID found in subject or previous emails");
            }

            $fromAddress = $message->getFrom()[0]->mail;
            $toAddress = config('mail.mailers.ai_mail.from.address');

            // Client ve tipi, Test modu icin
            $client = null;
            $clientType = null;
            $originalClientEmail = null;

            if ($testMode) {
                // Test moduysa map: tum verileri dogru isler ama mail gonderirken alirken test maili kullanilir
                $mappingResult = $this->mapTestEmailToClient($fromAddress);
                if ($mappingResult) {
                    $client = $mappingResult['client'];
                    $clientType = $mappingResult['client_type'];
                    $originalClientEmail = $mappingResult['original_email'];
                    $clientTypeLabel = $clientType == 1 ? 'Investor' : 'Client';
                    $this->info("🧪 Test Mode: Mapped {$fromAddress} to {$clientTypeLabel} ID: {$client->id} (Original: {$originalClientEmail})");
                } else {
                    $this->warn("⚠️ Test Mode: Could not map test email {$fromAddress} to any client");
                }
            } else {
                $inquiryRecord = CustomerEmailReply::where('to_address', $fromAddress)
                    ->where('is_outgoing', 1)
                    ->orderBy('created_at', 'desc')
                    ->first(['client_id', 'client_type']);

                if ($inquiryRecord) {
                    $clientId = $inquiryRecord->client_id;
                    $clientType = $inquiryRecord->client_type;

                    if ($clientType == 1) {
                        $client = \App\Models\Investors::find($clientId);
                    } else {
                        $client = \App\Models\Client::find($clientId);
                    }
                } else {
                    
                    $client = \App\Models\Client::where('email', $fromAddress)->first();
                    $investor = \App\Models\Investors::where('email', $fromAddress)->first();

                    if ($investor && !$client) {
                        $client = $investor;
                        $clientId = $investor->id;
                        $clientType = 1;
                    } elseif ($client && !$investor) {
                        $clientId = $client->id;
                        $clientType = 2;
                    } elseif ($client && $investor) {
                        $clientId = $client->id;
                        $clientType = 2;
                    } else {
                        return;
                    }
                }
            }

            // Verify we have a valid client and client type
            if (!$client || !$clientType) {
                $this->error("❌ Could not identify client for email: {$fromAddress}");
                return;
            }

            $clientTypeLabel = $clientType == 1 ? 'Investor' : 'Client';

            

            $emailRecord = CustomerEmailReply::create([
                'subject' => $subject,
                'from_address' => $fromAddress,
                'to_address' => $toAddress,
                'message' => $body,
                'is_outgoing' => 0,
                'response_sent' => 0,
                'inquiry_id' => $inquiryId,
                'property_id' => $propertyId,
                'client_id' => $client?->id,
                'client_type' => $clientType,
                'terms_accepted' => false
            ]);
            
            // gecmmis 3 mail
            $previousEmails = DB::table('ai_customer_email_replies')
                ->select(['id', 'from_address', 'to_address', 'message', 'is_outgoing', 'created_at', 'client_id'])
                ->where(function ($query) use ($emailRecord) {
                    $query->where('from_address', $emailRecord->from_address)
                          ->orWhere('to_address', $emailRecord->from_address);
                })
                ->where('id', '!=', $emailRecord->id)
                ->orderBy('created_at', 'desc')
                ->take(3)
                ->get();

            // son geleni de al
            $currentEmailData = [
                'from' => $fromAddress,
                'to' => $toAddress,
                'message' => $body,
                'is_outgoing' => 0,
                'created_at' => now()->toDateTimeString()
            ];

            $conversationHistory = $previousEmails->map(function($email) {
                return [
                    'from' => $email->from_address,
                    'to' => $email->to_address,
                    'message' => $email->message,
                    'is_outgoing' => $email->is_outgoing,
                    'created_at' => \Carbon\Carbon::parse($email->created_at)->toDateTimeString()
                ];
            })->toArray();

            array_unshift($conversationHistory, $currentEmailData);

            // tekrar subject islemi (gerekli mi bakalim yukarida var)
            $subject = mb_convert_encoding($subject, 'UTF-8', mb_detect_encoding($subject));
            $subject = preg_replace('/[^\p{L}\p{N}\s\-_.:]/u', '', $subject);

            // onceden CC istenmisse onu al
            $previousCCs = CustomerEmailReply::where(function($query) use ($fromAddress) {
                $query->where('from_address', $fromAddress)
                      ->orWhere('to_address', $fromAddress);
            })
            ->whereNotNull('cc_addresses')
            ->orderBy('created_at', 'desc')
            ->first();
            $cc = $previousCCs ? json_decode($previousCCs->cc_addresses, true) : null;

            // Prepare data for AI processing
            $emailData = [
                'from_address' => $fromAddress,
                'subject' => $subject,
                'message' => $body,
                'previous_conversations' => $conversationHistory,
                'cc_addresses' => $cc,
                'property_id' => $emailRecord->property_id,
                'inquiry_id' => $emailRecord->inquiry_id,
                'client' => $client ? [
                    'id' => $client->id,
                    'gender' => $client->gender,
                    'first_name' => $client->first_name ?? $client->name ?? null,
                    'surname' => $client->surname,
                    'email' => $client->email
                ] : null,
                'client_type' => $clientType,
                'appointment_booked' => $isBooked
            ];



            // Add only busy periods to emailData if we have a property_id
            if ($emailRecord->property_id) {
                $emailData['agent_busy_periods'] = $this->calendarAvailabilityService->getAgentBusyPeriods($emailRecord->property_id);
                $emailData['working_hours'] = [
                    'start' => '08:00',
                    'end' => '22:00'
                ];
            }

            // DetectIntent analysis before AI response generation
            if ($client && $emailRecord->inquiry_id) {
                $this->info("🔍 Analyzing for preference changes...");
                $analysisResult = $this->detectIntentService->analyzeEmailContent($emailData);

                if ($analysisResult && $analysisResult['wants_preference_change'] && $analysisResult['confidence'] >= 0.9) {
                    $this->info("✅ Preference change detected with confidence: " . ($analysisResult['confidence'] * 100) . "%");

                    if ($this->detectIntentService->processPreferenceChange($analysisResult, $emailData)) {
                        $this->info("🔄 Preferences updated, old matches cleaned, new matching started");
                        // Stop current inquiry processing since preferences changed
                        return;
                    }
                } else {
                    $this->info("ℹ️ No preference change detected or confidence too low");
                }
            }

            $this->info("🤖 Generating AI response...");

            // AI cagir maili olustur
            $response = $this->aiMailService->generateEmailContent($emailData);

            if (!$response) {
                throw new \Exception("AI donmedi");
            }

            // yapay zeka bazen 'response' diye donuyor
            $responseData = $response['response'] ?? $response;

            // bazen 'cc' bazen 'cc_addresses' olarak donuyor, duzelt
            $responseData['cc'] = $responseData['cc'] ?? $responseData['cc_addresses'] ?? [];
            unset($responseData['cc_addresses']);

            // Randevu icin gerekli islemleri yap
            $responseData = $this->aiMailService->handleAppointmentSuggestion(
                $responseData,
                [
                    'client_id' => $client?->id,
                    'property_id' => $emailRecord->property_id,
                    'inquiry_id' => $emailRecord->inquiry_id,
                ]
            );

            $appointmentStatus = $responseData['appointment_status'] ?? ['type' => 'NONE', 'datetime' => null];

            // Handle suggested_slots for SUGGESTING_TIMES status
            $appointmentDateTime = null;
            $suggestedSlots = null;

            if ($appointmentStatus['type'] === 'SUGGESTING_TIMES') {

                $suggestedSlots = $appointmentStatus['suggested_slots'] ?? [];
                Log::debug('AI suggested appointment times', [
                    'suggested_slots' => $suggestedSlots,
                    'inquiry_id' => $emailRecord->inquiry_id
                ]);
            } else {
                // For other statuses, use the datetime field
                $appointmentDateTime = $appointmentStatus['datetime'] ?? null;
            }

            // test mod mapping
            $toEmail = $testMode ? $fromAddress : $fromAddress;

            if ($testMode) {
                $this->info("🧪 Test Modu: {$toEmail} (original: {$fromAddress})");
            }

            // AI maili gonder
            $success = $this->aiMailService->sendAiGeneratedEmail(
                $toEmail,
                $subject,
                [
                    'email' => $responseData['email'],
                    'attachments' => $responseData['attachments'] ?? [],
                    'cc' => $responseData['cc'] ?? [],
                    'appointment' => $appointmentDateTime, // Save exact datetime (null for SUGGESTING_TIMES)
                    'appointment_status' => $appointmentStatus, // Save full appointment status object
                    'agent_id' => $responseData['agent_id'] ?? null,
                    'client_id' => $client?->id,
                    'property_id' => $emailRecord->property_id,
                    'conversation_summary' => $responseData['conversation_summary'] ?? null,
                ]
            );


            if (!$success) {
                throw new \Exception("Failed to send email - sendAiGeneratedEmail returned false");
            }

            $this->info("✅ Response sent successfully to {$toEmail}");

            // Email'i okundu olarak işaretle
            $message->setFlag(['Seen']);
            $this->info("👀 Orijinal email okundu olarak işaretlendi");


        } catch (\Exception $e) {
            Log::error("Email processing failed", [
                'subject' => $subject,  // Use the cleaned subject here too
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ]);
            $this->error("❌ Error processing email: " . $e->getMessage());
        }
    }

    /**
     * Extract inquiry ID from subject
     *
     * @param string $subject
     * @return int|null
     */
    private function extractInquiryId(string $subject): ?int
    {
        if (preg_match('/\[#(\d+)\]/', $subject, $matches)) {
            return (int) $matches[1];
        }
        return null;
    }

    /**
     * Extract property ID from subject or previous correspondence
     *
     * @param string $subject
     * @return int|null
     */
    private function extractPropertyId(string $subject): ?int
    {
        // If we have an inquiry ID, try to get property ID from previous correspondence
        if ($inquiryId = $this->extractInquiryId($subject)) {
            $previousEmail = CustomerEmailReply::where('inquiry_id', $inquiryId)
                ->whereNotNull('property_id')
                ->orderBy('created_at', 'desc')
                ->first();
            
            if ($previousEmail && $previousEmail->property_id) {
                return $previousEmail->property_id;
            }
        }
        return null;
    }

    /**
     * Map test email back to real client for test mode
     *
     * @param string $testEmail
     * @return array|null
     */
    protected function mapTestEmailToClient(string $testEmail): ?array
    {

        if (!$this->testEmailService->isTestEmail($testEmail)) {
            return null;
        }

        // Find the most recent CustomerEmailReply record that was sent to this test email
        // This will help us identify which real client this test email represents
        $recentEmail = CustomerEmailReply::where('to_address', $testEmail)
            ->whereNotNull('client_id')
            ->whereNotNull('client_type')
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$recentEmail) {
            $this->warn("⚠️ No previous email record found for test email: {$testEmail}");
            return null;
        }

        // Get the real client based on the stored client_id and client_type
        $clientType = $recentEmail->client_type;
        $clientId = $recentEmail->client_id;

        if ($clientType == 1) {
            // Investor
            $client = \App\Models\Investors::find($clientId);
        } else {
            // Regular client
            $client = \App\Models\Client::find($clientId);
        }

        if (!$client) {
            $this->warn("⚠️ Client not found for ID: {$clientId}, Type: {$clientType}");
            return null;
        }

        return [
            'client' => $client,
            'client_type' => $clientType,
            'original_email' => $client->email
        ];
    }

    /**
     * Get client type from existing conversation context
     *
     * @param string $fromAddress
     * @param string|null $inquiryId
     * @param bool $inquiryIdFromSubject Whether inquiry ID came from subject (more reliable) or fallback lookup
     * @return int|null
     */
    private function getClientTypeFromContext($fromAddress, $inquiryId = null, $inquiryIdFromSubject = false)
    {
        $inquirySource = $inquiryIdFromSubject ? 'subject' : 'fallback lookup';
        $this->info("🔍 Looking up client_type for {$fromAddress}, inquiry_id: " . ($inquiryId ?? 'null') . " (from {$inquirySource})");

        // First, try to get client_type from existing conversation by inquiry_id
        if ($inquiryId) {
            // If inquiry ID came from subject, it's authoritative - use it directly
            if ($inquiryIdFromSubject) {
                $existingEmail = CustomerEmailReply::where('inquiry_id', $inquiryId)
                    ->whereNotNull('client_type')
                    ->orderBy('created_at', 'desc')
                    ->first();

                if ($existingEmail && $existingEmail->client_type) {
                    $this->info("✅ Found client_type {$existingEmail->client_type} by inquiry_id {$inquiryId} (from subject - authoritative)");
                    return $existingEmail->client_type;
                } else {
                    $this->info("❌ No client_type found by inquiry_id {$inquiryId} (from subject)");
                }
            } else {
                // If inquiry ID came from fallback lookup, be more careful
                // Make sure the email address matches the conversation
                $existingEmail = CustomerEmailReply::where('inquiry_id', $inquiryId)
                    ->where(function($query) use ($fromAddress) {
                        $query->where('from_address', $fromAddress)
                              ->orWhere('to_address', $fromAddress);
                    })
                    ->whereNotNull('client_type')
                    ->orderBy('created_at', 'desc')
                    ->first();

                if ($existingEmail && $existingEmail->client_type) {
                    $this->info("✅ Found client_type {$existingEmail->client_type} by inquiry_id {$inquiryId} (from fallback - verified email match)");
                    return $existingEmail->client_type;
                } else {
                    $this->info("❌ No client_type found by inquiry_id {$inquiryId} with matching email {$fromAddress} (from fallback)");
                }
            }
        }

        // If no inquiry_id or no existing conversation, try by email address
        // Look for previous emails where this address was either sender or recipient
        $existingEmail = CustomerEmailReply::where(function($query) use ($fromAddress) {
                $query->where('from_address', $fromAddress)
                      ->orWhere('to_address', $fromAddress);
            })
            ->whereNotNull('client_type')
            ->orderBy('created_at', 'desc')
            ->first();

        if ($existingEmail && $existingEmail->client_type) {
            $this->info("✅ Found client_type {$existingEmail->client_type} by email address {$fromAddress}");
            return $existingEmail->client_type;
        } else {
            $this->info("❌ No client_type found by email address {$fromAddress}");
        }

        return null; // No existing context found
    }

    /**
     * Determine which folder to use based on options and test mode
     */
    protected function determineFolderName(bool $testMode): string
    {
        // klasor belirlendiyse kullan
        if ($this->option('folder') && $this->option('folder') !== 'INBOX') {
            return $this->option('folder');
        }

        // auto-folder secenegi varsa ona gore
        if ($this->option('auto-folder')) {
            return $testMode ? 'KI-TEST' : 'KI-PRODUCTION';  // Use hyphen, not underscore
        }

        // Default INBOX
        return 'INBOX';
    }

    /**
     * List all available IMAP folders
     */
    protected function listFolders(): int
    {
        try {
            $client = Client::account('ai_mail');
            $client->connect();

            $this->info('📁 Bulunan IMAP Klasorler:');

            $folders = $client->getFolders();
            foreach ($folders as $folder) {
                try {
                    // Try to get counts safely
                    $unreadCount = 0;
                    $totalCount = 0;

                    try {
                        $totalCount = $folder->query()->count();
                    } catch (\Exception $e) {
                        $this->warn("Could not get total count for {$folder->name}: " . $e->getMessage());
                    }

                    try {
                        $unreadCount = $folder->query()->unseen()->count();
                    } catch (\Exception $e) {
                        $this->warn("Could not get unread count for {$folder->name}: " . $e->getMessage());
                    }

                    $this->line(sprintf(
                        "📂 %-20s (Unread: %s, Total: %s)",
                        $folder->name,
                        $unreadCount !== 0 ? $unreadCount : '?',
                        $totalCount !== 0 ? $totalCount : '?'
                    ));

                } catch (\Exception $e) {
                    $this->line(sprintf(
                        "📂 %-20s (Error: %s)",
                        $folder->name ?? 'Unknown',
                        $e->getMessage()
                    ));
                }
            }

            $this->info('ornekler:');
            $this->info('php artisan email:fetch --folder=INBOX --test-mode');
            $this->info('php artisan email:fetch --folder=AI_TEST --test-mode');
            $this->info('php artisan email:fetch --auto-folder --test-mode');

            return 0;

        } catch (\Exception $e) {
            $this->error("❌ baglanamadi: " . $e->getMessage());
            $this->error("💡 Check your IMAP configuration in .env:");
            $this->error("   AI_IMAP_HOST, AI_IMAP_USERNAME, AI_IMAP_PASSWORD");
            return 1;
        }
    }

    /**
     * List folder names only (no message counts)
     */
    protected function listFoldersSimple(): int
    {
        try {
            $client = Client::account('ai_mail');
            $client->connect();

            $this->info('📁 IMAP(Simple List):');

            $folders = $client->getFolders();
            foreach ($folders as $folder) {
                $this->line("📂 " . $folder->name);
            }

            $this->info('');
            $this->info('💡 Usage Examples:');
            $this->info('php artisan email:fetch --folder=INBOX --test-mode');
            $this->info('php artisan email:fetch --folder=' . ($folders->first()->name ?? 'INBOX') . ' --test-mode');

            return 0;

        } catch (\Exception $e) {
            $this->error("❌ Failed to list folders: " . $e->getMessage());
            $this->error("check AI_IMAP_HOST, AI_IMAP_USERNAME, AI_IMAP_PASSWORD");
            return 1;
        }
    }

    /**
     * Remove content after a specific pattern in a string
     *
     * @param string $string
     * @param string $pattern
     * @return string
     */
    private function removeAfterPattern(string $string, string $pattern): string
    {
        // Find the position of the pattern
        $position = strpos($string, $pattern);

        // If pattern is found, cut the string up to that position
        if ($position !== false) {
            return substr($string, 0, $position);
        }

        // If pattern is not found, return the original string
        return $string;
    }
}
