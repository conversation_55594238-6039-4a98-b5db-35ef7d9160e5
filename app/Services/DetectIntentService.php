<?php

namespace App\Services;

use App\Models\Ai\AiPreference;
use App\Models\Ai\CustomerEmailReply;
use App\Models\Client;
use App\Models\Investors;
use App\Models\Distance\DistanceBuyer;
use App\Models\Distance\DistanceBuyerAddress;
use App\Models\Distance\DistanceMatchResults;
use App\Models\BuyerPropertyPreferences;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Artisan;

class DetectIntentService extends BaseAiService
{
    public function analyzeEmailContent(array $emailData): ?array
    {
        $systemPrompt = <<<'EOT'
You are an AI assistant that analyzes customer emails to detect when they want to change their property preferences.

Your task is to determine if the customer is expressing a desire to change their property search criteria (location, price range, property type, etc.).

IMPORTANT: Only detect preference changes when you are CERTAIN the customer wants different criteria. Do NOT detect changes when:
- Customer is showing interest in current properties
- Customer is asking questions about current offerings
- Customer is engaged with current properties
- Customer is scheduling appointments for current properties

ONLY detect preference changes when customer explicitly states they want:
- Different location/area
- Different price range
- Different property type
- Different size requirements
- Different specific criteria

Return JSON with this structure:
{
    "wants_preference_change": true/false,
    "confidence": 0.0-1.0,
    "summary": "Brief summary of what customer wants to change",
    "reason": "Detailed explanation of why you detected this change",
    "new_preferences": {
        "location": "new location if mentioned",
        "price_min": number or null,
        "price_max": number or null,
        "property_type": "new type if mentioned",
        "rooms": "room requirements if mentioned",
        "other_criteria": "any other specific requirements"
    }
}

If confidence is below 0.9, set wants_preference_change to false.
EOT;

        $userMessage = "Customer email: " . $emailData['message'];
        if (!empty($emailData['previous_conversations'])) {
            $userMessage .= "\n\nPrevious conversation context:\n";
            foreach ($emailData['previous_conversations'] as $conv) {
                $type = $conv['is_outgoing'] ? 'AI' : 'Customer';
                $userMessage .= "$type: " . substr($conv['message'], 0, 200) . "\n";
            }
        }

        try {
            $response = $this->makeRequest($systemPrompt, $userMessage, [
                'response_format' => ['type' => 'json_object']
            ]);

            $result = json_decode($response, true);
            if (!$result || !isset($result['wants_preference_change'])) {
                return null;
            }

            return $result;
        } catch (\Exception $e) {
            return null;
        }
    }

    public function processPreferenceChange(array $analysisResult, array $emailData): bool
    {
        if (!$analysisResult['wants_preference_change'] || $analysisResult['confidence'] < 0.9) {
            return false;
        }

        // Find the buyer record
        $buyer = $this->findBuyer($emailData['client']['id'], $emailData['client_type']);
        if (!$buyer) {
            return false;
        }

        try {
            DB::transaction(function () use ($analysisResult, $emailData, $buyer) {
                AiPreference::create([
                    'inquiry_id' => $emailData['inquiry_id'],
                    'client_id' => $emailData['client']['id'],
                    'buyer_id' => $buyer->id,
                    'client_type' => $emailData['client_type'],
                    'summary' => $analysisResult['summary'],
                    'reason' => $analysisResult['reason'],
                    'preferences_json' => $analysisResult['new_preferences'],
                    'confidence' => $analysisResult['confidence']
                ]);

                $this->updateClientPreferences($buyer, $analysisResult['new_preferences']);
                $this->cleanOldMatches($buyer);
            });

            $this->notifyManager($analysisResult, $emailData);
            $this->runPropertyMatching($emailData['client']['id']);

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    protected function findBuyer(int $clientId, int $clientType): ?DistanceBuyer
    {
        // client_type: 1=Investor, 2=Client
        // prospective_id: >0 for Clients, =0 for Investors
        if ($clientType == 1) {
            // Investor
            return DistanceBuyer::where('client_id', $clientId)
                ->where('prospective_id', 0)
                ->first();
        } else {
            // Client
            return DistanceBuyer::where('client_id', $clientId)
                ->where('prospective_id', '>', 0)
                ->first();
        }
    }

    public function updateClientPreferences(DistanceBuyer $buyer, array $newPreferences): void
    {
        if (!empty($newPreferences['location'])) {
            DistanceBuyerAddress::where('buyer_id', $buyer->id)->delete();

            DistanceBuyerAddress::create([
                'buyer_id' => $buyer->id,
                'city' => $newPreferences['location'],
                'radius' => 50
            ]);
        }

        $preferences = BuyerPropertyPreferences::where('distance_buyer_id', $buyer->id)->first();
        if ($preferences) {
            if (isset($newPreferences['price_min']) && isset($newPreferences['price_max'])) {
                $preferences->price = $newPreferences['price_min'] . '-' . $newPreferences['price_max'];
            }
            if (!empty($newPreferences['rooms'])) {
                $preferences->room_number = $newPreferences['rooms'];
            }
            $preferences->save();
        }
    }

    public function cleanOldMatches(DistanceBuyer $buyer): void
    {
        DistanceMatchResults::where('buyer_id', $buyer->id)->delete();
    }

    public function notifyManager(array $analysisResult, array $emailData): void
    {
        $managerEmail = env('AI_MANAGER_EMAIL', '<EMAIL>');
        $clientName = $emailData['client']['first_name'] . ' ' . $emailData['client']['surname'];
        
        $subject = 'Customer Preference Change Detected - ' . $clientName;
        $content = "
        <h3>Customer Preference Change Detected</h3>
        <p><strong>Customer:</strong> {$clientName} ({$emailData['client']['email']})</p>
        <p><strong>Inquiry ID:</strong> {$emailData['inquiry_id']}</p>
        <p><strong>Confidence:</strong> " . ($analysisResult['confidence'] * 100) . "%</p>
        <p><strong>Summary:</strong> {$analysisResult['summary']}</p>
        <p><strong>Reason:</strong> {$analysisResult['reason']}</p>
        <p><strong>New Preferences:</strong></p>
        <ul>";
        
        foreach ($analysisResult['new_preferences'] as $key => $value) {
            if ($value) {
                $content .= "<li><strong>{$key}:</strong> {$value}</li>";
            }
        }
        
        $content .= "</ul>";

        Mail::send([], [], function ($message) use ($managerEmail, $subject, $content) {
            $message->to($managerEmail)
                    ->subject($subject)
                    ->html($content);
        });
    }

    protected function runPropertyMatching(int $clientId): void
    {
        try {
            Artisan::call('match:properties', ['--client-id' => $clientId]);
            Artisan::call('ai:check-new-matches', ['--limit' => 1, '--unique' => 1]);
        } catch (\Exception $e) {
            // Silent fail
        }
    }
}
