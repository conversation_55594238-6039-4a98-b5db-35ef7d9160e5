You are analyzing a customer email to detect if they want to change their property preferences.

Current inquiry context:
- Customer: {customer_email}
- Current property type: {current_property_type}
- Current location: {current_location}
- Current price range: {current_price_range}

Customer email content:
"{email_content}"

CRITICAL SAFETY CHECKS:
1. DO NOT detect intent to change if customer shows interest in current properties (asking questions, requesting viewings, positive feedback)
2. DO NOT change preferences if customer is engaged with current offerings
3. ONLY detect intent when customer EXPLICITLY states they want DIFFERENT property criteria

Analyze if the customer is expressing a desire to change their property search preferences. Look for explicit mentions of:
- "I want a different type of property" 
- "I'm looking for houses instead of apartments"
- "I need to change my location to..."
- "My budget has changed to..."

IMPORTANT: Only detect intent changes when you are CERTAIN the customer wants to change preferences AND is not showing interest in current properties.

Return a JSON response with this exact structure:
{
  "intent_detected": true/false,
  "customer_showing_interest": true/false,
  "summary": "Brief description of what customer wants to change",
  "reason": "Why the customer wants to make this change",
  "preference_changes": {
    "property_type": "new type if mentioned",
    "location": "new location if mentioned", 
    "price_range": "new range if mentioned",
    "other": "any other changes"
  },
  "stop_current_inquiry": true/false,
  "confidence_score": 0.0-1.0,
  "uncertainty_reason": "If uncertain, explain why you're not confident"
}

Set "intent_detected" to FALSE if:
- Customer shows ANY interest in current properties
- Customer is asking questions about current offerings
- You have ANY uncertainty about their intent
- Confidence score < 0.9

Only set "stop_current_inquiry" to true when absolutely certain customer wants completely different property criteria.
